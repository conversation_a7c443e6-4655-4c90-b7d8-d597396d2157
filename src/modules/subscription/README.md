# Subscription System

This module provides a comprehensive subscription limits system that controls access to features and resources based on subscription tiers.

## Subscription Tiers

- **Free**: 3 workers, 1 project, 0 managers, basic features only
- **Small Business**: 15 workers, 3 projects, 2 managers, includes web version and photo confirmation
- **Medium**: 50 workers, 10 projects, 5 managers, includes all features
- **Enterprise**: Unlimited resources, all features

## Usage

### 1. Feature-based Restrictions

Use decorators to restrict access to features based on subscription:

```typescript
import { RequirePhotoConfirmation, RequireWebVersion } from '@/common/decorators/subscription.decorator';
import { SubscriptionGuard } from '@/common/guards/subscription.guard';

@Controller('some-feature')
@UseGuards(JwtAuthGuard, RolesGuard, SubscriptionGuard)
export class SomeController {
  
  @Post('photo-validation')
  @RequirePhotoConfirmation()
  createPhotoValidation() {
    // This endpoint requires photo confirmation feature
  }

  @Get('web-dashboard')
  @RequireWebVersion()
  getWebDashboard() {
    // This endpoint requires web version access
  }
}
```

### 2. Resource Limit Restrictions

Use decorators to check resource limits before creation:

```typescript
import { CheckWorkersLimit, CheckProjectsLimit } from '@/common/decorators/subscription.decorator';

@Controller('workers')
@UseGuards(JwtAuthGuard, RolesGuard, SubscriptionGuard)
export class WorkersController {
  
  @Post()
  @CheckWorkersLimit()
  createWorker() {
    // This will check if the partner can add more workers
  }
}
```

### 3. Available Decorators

#### Feature Decorators
- `@RequireWebVersion()`
- `@RequirePhotoConfirmation()`
- `@RequireFinancialStatistics()`
- `@RequireCharts()`
- `@RequireAdvancedAnalytics()`
- `@RequireReportGeneration()`

#### Resource Limit Decorators
- `@CheckWorkersLimit(currentCount?)`
- `@CheckProjectsLimit(currentCount?)`
- `@CheckManagersLimit(currentCount?)`

### 4. Programmatic Usage

You can also use the subscription service directly:

```typescript
import { SubscriptionService } from '@/modules/subscription/subscription.service';
import { SubscriptionFeature, SubscriptionResource } from '@/common/enums/subscription.enum';

@Injectable()
export class SomeService {
  constructor(private subscriptionService: SubscriptionService) {}

  async someMethod(partnerId: string) {
    // Check feature availability
    const hasPhotoFeature = await this.subscriptionService.isFeatureAvailable(
      partnerId, 
      SubscriptionFeature.PhotoConfirmation
    );

    // Check resource limits
    const canAddWorker = await this.subscriptionService.canAddResource(
      partnerId,
      SubscriptionResource.Workers,
      currentWorkerCount
    );

    // Enforce limits (throws exceptions if not allowed)
    await this.subscriptionService.enforceFeatureAvailability(
      partnerId,
      SubscriptionFeature.PhotoConfirmation
    );
  }
}
```

### 5. Error Handling

The system throws specific exceptions for different limit violations:

- `WorkersLimitExceededException`
- `ProjectsLimitExceededException`
- `ManagersLimitExceededException`
- `PhotoConfirmationNotAvailableException`
- `WebVersionNotAvailableException`
- etc.

These exceptions are automatically handled by the global exception filter and return appropriate HTTP responses.

### 6. Database Schema

The subscription tier is stored in the `partners` table:

```sql
ALTER TABLE partner ADD COLUMN subscription_tier subscription_tier NOT NULL DEFAULT 'free';
```

### 7. Mock Data

For testing, you can update a partner's subscription tier:

```sql
UPDATE partner SET subscription_tier = 'enterprise' WHERE id = 'partner-id';
```

## Implementation Notes

- The system automatically detects the user's partner ID based on their role
- For partners: uses their own partner record
- For managers/workers: looks up their associated partner
- Resource counts are calculated in real-time from the database
- All limits are enforced at the API level before business logic execution
