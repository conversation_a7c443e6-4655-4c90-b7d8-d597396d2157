import { Module, forwardRef } from '@nestjs/common';

import { PartnersModule } from '../partners/partners.module';
import { ManagersModule } from '../managers/managers.module';
import { WorkersModule } from '../workers/workers.module';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionExamplesController } from './examples/subscription-examples.controller';

@Module({
  imports: [
    forwardRef(() => PartnersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => WorkersModule),
  ],
  controllers: [SubscriptionController, SubscriptionExamplesController],
  providers: [SubscriptionService],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}
