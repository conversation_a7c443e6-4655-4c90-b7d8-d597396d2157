-- Migration: Add subscription tier to partners table
-- This migration adds the subscription_tier column to the partner table

-- Create the subscription_tier enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE subscription_tier AS ENUM ('free', 'small_business', 'medium', 'enterprise');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add the subscription_tier column to the partner table
ALTER TABLE partner 
ADD COLUMN IF NOT EXISTS subscription_tier subscription_tier NOT NULL DEFAULT 'free';

-- Create an index on subscription_tier for better query performance
CREATE INDEX IF NOT EXISTS idx_partner_subscription_tier ON partner(subscription_tier);

-- Update existing partners to have 'free' tier (this is already the default)
-- UPDATE partner SET subscription_tier = 'free' WHERE subscription_tier IS NULL;

-- Example: Set some partners to different tiers for testing
-- UPDATE partner SET subscription_tier = 'enterprise' WHERE company_name LIKE '%Enterprise%';
-- UPDATE partner SET subscription_tier = 'medium' WHERE company_name LIKE '%Medium%';
-- UPDATE partner SET subscription_tier = 'small_business' WHERE company_name LIKE '%Small%';
