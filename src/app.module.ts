import { ThrottlerStorageRedisService } from '@nest-lab/throttler-storage-redis';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_PIPE } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import Redis from 'ioredis';
import { ZodValidationPipe } from 'nestjs-zod';

import { AppController } from './app.controller';
import { GlobalExceptionFilter } from './common/filters/http-exception.filter';
import { JwtExceptionFilter } from './common/filters/jwt-exception.filter';
import { CustomThrottlerGuard } from './common/guards/throttler.guard';
import { RequestLoggerMiddleware } from './common/middleware/request-logger.middleware';
import authConfig from './config/auth.config';
import dbConfig from './config/db.config';
import firebaseConfig from './config/firebase.config';
import frontendConfig from './config/frontend.config';
import redisConfig from './config/redis.config';
import s3Config from './config/s3.config';
import { ActivityHistoryModule } from './modules/activity-history/activity-history.module';
import { AuthModule } from './modules/auth/auth.module';
import { CredentialVerificationModule } from './modules/credential-verification/credential-verification.module';
import { DailyReportsModule } from './modules/daily-reports/daily-reports.module';
import { DatabaseModule } from './modules/db/db.module';
import { DevicesModule } from './modules/devices/devices.module';
import { EmailModule } from './modules/email/email.module';
import { FilePermissionsModule } from './modules/file-permissions/file-permissions.module';
import { FilesModule } from './modules/files/files.module';
import { ManagersModule } from './modules/managers/managers.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { PartnersModule } from './modules/partners/partners.module';
import { PasswordResetRequestsModule } from './modules/password-reset-requests/password-reset-requests.module';
import { PauseHistoryModule } from './modules/pause-history/pause-history.module';
import { PresenceValidationsModule } from './modules/presence-validations/presence-validations.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { RedisModule } from './modules/redis/redis.module';
import { RegistrationCodesModule } from './modules/registration-codes/registration-codes.module';
import { RegistrationRequestsModule } from './modules/registration-requests/registration-requests.module';
import { SecurityNotificationsModule } from './modules/security-notifications/security-notifications.module';
import { UpdatesModule } from './modules/updates/updates.module';
import { UsersModule } from './modules/users/users.module';
import { WorkersModule } from './modules/workers/workers.module';

@Module({
  imports: [
    DatabaseModule,
    AuthModule,
    RegistrationCodesModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        authConfig,
        dbConfig,
        s3Config,
        firebaseConfig,
        frontendConfig,
        redisConfig,
      ],
    }),
    ScheduleModule.forRoot(),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            name: 'default',
            ttl: 60000,
            limit: 200,
          },
        ],
        storage: new ThrottlerStorageRedisService(
          new Redis({
            host: configService.get('redis.host'),
            port: configService.get('redis.port'),
            password: configService.get('redis.password'),
          }),
        ),
      }),
    }),
    ActivityHistoryModule,
    PresenceValidationsModule,
    DailyReportsModule,
    FilesModule,
    ManagersModule,
    NotificationsModule,
    PartnersModule,
    PauseHistoryModule,
    DevicesModule,
    PasswordResetRequestsModule,
    ProjectsModule,
    EmailModule,
    CredentialVerificationModule,
    FilePermissionsModule,
    RedisModule,
    RegistrationRequestsModule,
    UpdatesModule,
    SecurityNotificationsModule,
    UsersModule,
    WorkersModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: JwtExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware);
  }
}
