import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import {
  SubscriptionFeature,
  SubscriptionResource,
} from '@/common/enums/subscription.enum';
import { SubscriptionService } from '@/modules/subscription/subscription.service';
import { PartnersService } from '@/modules/partners/partners.service';
import { ManagersService } from '@/modules/managers/managers.service';
import { WorkersService } from '@/modules/workers/workers.service';

import {
  SUBSCRIPTION_FEATURE_KEY,
  SUBSCRIPTION_RESOURCE_KEY,
  SubscriptionFeatureMetadata,
  SubscriptionResourceMetadata,
} from '../decorators/subscription.decorator';

@Injectable()
export class SubscriptionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly subscriptionService: SubscriptionService,
    private readonly partnersService: PartnersService,
    private readonly managersService: ManagersService,
    private readonly workersService: WorkersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const featureMetadata =
      this.reflector.getAllAndOverride<SubscriptionFeatureMetadata>(
        SUBSCRIPTION_FEATURE_KEY,
        [context.getHandler(), context.getClass()],
      );

    const resourceMetadata =
      this.reflector.getAllAndOverride<SubscriptionResourceMetadata>(
        SUBSCRIPTION_RESOURCE_KEY,
        [context.getHandler(), context.getClass()],
      );

    // If no subscription metadata is set, allow access
    if (!featureMetadata && !resourceMetadata) {
      return true;
    }

    const request: Request = context.switchToHttp().getRequest();
    const { user } = request;

    if (!user) {
      return false;
    }

    // Get partner ID based on user role
    let partnerId: string;

    if (user.role === 'partner') {
      const partner = await this.partnersService.findOneByUserId(user.id);
      if (!partner) return false;
      partnerId = partner.id;
    } else if (user.role === 'manager' || user.role === 'worker') {
      // For managers and workers, we need to get their partner ID
      if (!user.entityId) return false;

      if (user.role === 'manager') {
        partnerId = await this.managersService.getPartnerId(user.entityId);
        if (!partnerId) return false;
      } else {
        partnerId = await this.workersService.getPartnerId(user.entityId);
        if (!partnerId) return false;
      }
    } else {
      return false;
    }

    try {
      // Check feature availability
      if (featureMetadata) {
        await this.subscriptionService.enforceFeatureAvailability(
          partnerId,
          featureMetadata.feature,
        );
      }

      // Check resource limits
      if (resourceMetadata) {
        await this.subscriptionService.enforceResourceLimit(
          partnerId,
          resourceMetadata.resource,
          resourceMetadata.currentCount,
        );
      }

      return true;
    } catch (error) {
      // The subscription service will throw appropriate exceptions
      // which will be handled by the global exception filter
      throw error;
    }
  }
}
